{"rustc": 1842507548689473721, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 9656904095642909417, "path": 8101555014842407130, "deps": [[5820056977320921005, "anstream", false, 17242775080647536335], [9394696648929125047, "anstyle", false, 9160333974298385333], [11166530783118767604, "strsim", false, 10316161204283729892], [11649982696571033535, "clap_lex", false, 15680049459825802750]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\clap_builder-72f33102f4c3e7f3\\dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}