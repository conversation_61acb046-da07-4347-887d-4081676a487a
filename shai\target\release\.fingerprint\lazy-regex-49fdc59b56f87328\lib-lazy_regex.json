{"rustc": 1842507548689473721, "features": "[\"default\", \"regex\"]", "declared_features": "[\"default\", \"lite\", \"perf\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"regex\", \"regex-lite\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "target": 373025063261422169, "profile": 2040997289075261528, "path": 7791883380217912071, "deps": [[3722963349756955755, "once_cell", false, 9505186314257102092], [9451456094439810778, "regex", false, 17849562051594462071], [15278928094109957391, "lazy_regex_proc_macros", false, 18147772682060591619]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\lazy-regex-49fdc59b56f87328\\dep-lib-lazy_regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}