{"rustc": 1842507548689473721, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2040997289075261528, "path": 15822400793770354601, "deps": [[555019317135488525, "regex_automata", false, 3138729129037455294], [2779309023524819297, "aho_corasick", false, 15403457526219750932], [9408802513701742484, "regex_syntax", false, 12461707605168070718], [15932120279885307830, "memchr", false, 7304780936792955656]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\regex-da6fa30a6c60fc1d\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}