{"rustc": 1842507548689473721, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 10369491684090452477, "path": 10812796613434319891, "deps": [[1009387600818341822, "matchers", false, 16973774183666566802], [1017461770342116999, "sharded_slab", false, 11212012797515897658], [1359731229228270592, "thread_local", false, 9917752564787729805], [3424551429995674438, "tracing_core", false, 1368024486778328964], [3666196340704888985, "smallvec", false, 806051679690730132], [3722963349756955755, "once_cell", false, 9505186314257102092], [6981130804689348050, "tracing_serde", false, 13097330840388911933], [8606274917505247608, "tracing", false, 8424581838115889242], [8614575489689151157, "nu_ansi_term", false, 9869315401239764383], [9451456094439810778, "regex", false, 17849562051594462071], [9689903380558560274, "serde", false, 746175189569263742], [10806489435541507125, "tracing_log", false, 13001338260836468017], [15367738274754116744, "serde_json", false, 4917936035312756425]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tracing-subscriber-e4ed26442d955d7c\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}