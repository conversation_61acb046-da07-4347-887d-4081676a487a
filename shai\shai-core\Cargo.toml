[package]
name = "shai-core"
version = "0.1.0"
edition = "2021"

[dependencies]
# New LLM module dependencies
async-trait = "0.1"
reqwest = { version = "0.12", features = ["json", "stream"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
uuid = { version = "1.0", features = ["v4"] }
tokio = { version = "1.0", features = ["full"] }
tokio-util = "0.7"
futures = "0.3"
termimad = "0.33"
tree-sitter = "0.24"
tree-sitter-highlight = "0.24"

# Tool system dependencies
schemars = "1.0.1"
shai-macros = { path = "../shai-macros" }
shai-llm = { path = "../shai-llm" }
openai_dive = "1.2"
regex = "1.0"
walkdir = "2.4"
chrono = { version = "0.4", features = ["serde"] }
thiserror = "2.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "fmt", "json"] }
tracing-appender = "0.2"
similar = "2.6"
fs = "0.0.5"
dirs = "6.0"

[dev-dependencies]
tempfile = "3.20.0"
paste = "1.0"

[lints.rust]
dead_code = "allow"
unused_variables = "allow"
#unused_mut = "allow"
#unused_imports = "allow"

