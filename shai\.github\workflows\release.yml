name: Release

on:
  release:
    types: [created]

jobs:
  build:
    name: Build for ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        include:
          - os: ubuntu-latest
            target: x86_64-unknown-linux-musl
            binary_name: shai
            asset_name: shai-linux-x86_64
          - os: windows-latest
            target: x86_64-pc-windows-msvc
            binary_name: shai.exe
            asset_name: shai-windows-x86_64.exe
          - os: macos-latest
            target: x86_64-apple-darwin
            binary_name: shai
            asset_name: shai-macos-x86_64
          - os: macos-latest
            target: aarch64-apple-darwin
            binary_name: shai
            asset_name: shai-macos-aarch64

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.target }}

      - name: Install dependencies (Linux)
        if: matrix.os == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y build-essential musl-tools

      - name: Add musl target (Linux)
        if: matrix.target == 'x86_64-unknown-linux-musl'
        run: rustup target add x86_64-unknown-linux-musl

      - name: Cache cargo registry
        uses: actions/cache@v4
        with:
          path: ~/.cargo/registry
          key: ${{ runner.os }}-cargo-registry-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            ${{ runner.os }}-cargo-registry-

      - name: Cache cargo index
        uses: actions/cache@v4
        with:
          path: ~/.cargo/git
          key: ${{ runner.os }}-cargo-index-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            ${{ runner.os }}-cargo-index-

      - name: Cache target directory
        uses: actions/cache@v4
        with:
          path: target
          key: ${{ runner.os }}-${{ matrix.target }}-target-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            ${{ runner.os }}-${{ matrix.target }}-target-

      - name: Build binary
        run: cargo build --release --target ${{ matrix.target }}

      - name: Prepare binary (Unix)
        if: matrix.os != 'windows-latest'
        run: |
          cp target/${{ matrix.target }}/release/${{ matrix.binary_name }} ${{ matrix.asset_name }}
          strip ${{ matrix.asset_name }}

      - name: Prepare binary (Windows)
        if: matrix.os == 'windows-latest'
        run: |
          cp target/${{ matrix.target }}/release/${{ matrix.binary_name }} ${{ matrix.asset_name }}

      - name: Upload binary to release
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ github.event.release.upload_url }}
          asset_path: ${{ matrix.asset_name }}
          asset_name: ${{ matrix.asset_name }}
          asset_content_type: application/octet-stream