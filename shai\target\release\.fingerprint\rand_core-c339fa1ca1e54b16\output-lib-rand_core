{"$message_type":"diagnostic","message":"found staticlib `std` instead of rlib or dylib","code":{"code":"E0462","explanation":"Found `staticlib` `..` instead of `rlib` or `dylib`.\n\nConsider the following two files:\n\n`a.rs`\n```ignore (cannot-link-with-other-tests)\n#![crate_type = \"staticlib\"]\n\nfn foo() {}\n```\n\n`main.rs`\n```ignore (cannot-link-with-other-tests)\nextern crate a;\n\nfn main() {\n    a::foo();\n}\n```\n\nCrate `a` is compiled as a `staticlib`. A `staticlib` is a system-dependant\nlibrary only intended for linking with non-Rust applications (C programs). Note\nthat `staticlib`s include all upstream dependencies (`core`, `std`, other user\ndependencies, etc) which makes them significantly larger than `dylib`s:\nprefer `staticlib` for linking with C programs. Learn more about different\n`crate_type`s in [this section of the Reference](../reference/linkage.html).\n\nThis error can be fixed by:\n * Using [Cargo](../cargo/index.html), the Rust package manager, automatically\n   fixing this issue.\n * Recompiling the crate as a `rlib` or `dylib`; formats suitable for Rust\n   linking.\n"},"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\rand_core-0.9.3\\src\\lib.rs","byte_start":1492,"byte_end":1509,"line_start":39,"line_end":39,"column_start":1,"column_end":18,"is_primary":true,"text":[{"text":"extern crate std;","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following crate versions were found:\ncrate `std`: C:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-msvc\\lib\\rustlib\\x86_64-pc-windows-msvc\\lib\\std-c85e9beb7923f636.dll.lib","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"please recompile that crate using --crate-type lib","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0462]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: found staticlib `std` instead of rlib or dylib\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\rand_core-0.9.3\\src\\lib.rs:39:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mextern crate std;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the following crate versions were found:\u001b[0m\n\u001b[0m           crate `std`: C:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-msvc\\lib\\rustlib\\x86_64-pc-windows-msvc\\lib\\std-c85e9beb7923f636.dll.lib\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: please recompile that crate using --crate-type lib\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 1 previous error\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0462`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0462`.\u001b[0m\n"}
