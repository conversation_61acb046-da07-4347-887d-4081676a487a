[package]
name = "shai"
version = "0.1.1"
edition = "2021"


[dependencies]
shai-core = { path = "../shai-core" }
shai-llm = { path = "../shai-llm" }
chrono = "0.4"
clap = { version = "4.0", features = ["derive"] }
tempfile = "3.20.0"
serde = { version = "1.0", features = ["derive"] }
rmp-serde = "1.1"
tokio = { version = "1.0", features = ["full"] }
crossterm = { version = "0.28", features = ["event-stream"] }
futures = "0.3"
ratatui = { git = "https://github.com/Marlinski/ratatui", branch = "feature/viewport-resize-v29", features = ["crossterm"] }
ansi-to-tui = "7.0.0"
# ratatui = { version = "0.29", features = ["crossterm"] }
tui-textarea = { version = "0.7", features = ["crossterm", "ratatui"] }
serde_json = "1.0"
figrs = "0.3"
rand = "0.9"
async-trait = "0.1"
console = "0.16"
ringbuffer = "0.16"
cli-clipboard = "0.4"
textwrap = "0.16"

[target.'cfg(unix)'.dependencies]
libc = "0.2"

[lints.rust]
dead_code = "allow"
unused_variables = "allow"
unused_mut = "allow"
unused_imports = "allow"