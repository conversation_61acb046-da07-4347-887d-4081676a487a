{"rustc": 1842507548689473721, "features": "[\"default\", \"is_variant\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 11796376952621915773, "profile": 3601279031184244129, "path": 10082790951843586865, "deps": [[3060637413840920116, "proc_macro2", false, 16258515178716128741], [10640660562325816595, "syn", false, 14485790238594888535], [17685210698997651194, "convert_case", false, 1839385696469357474], [17990358020177143287, "quote", false, 2917140431734547102]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\derive_more-impl-7b0c4c8717caba70\\dep-lib-derive_more_impl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}