{"rustc": 1842507548689473721, "features": "[\"default\", \"derive\", \"schemars_derive\", \"std\"]", "declared_features": "[\"_ui_test\", \"arrayvec07\", \"bigdecimal04\", \"bytes1\", \"chrono04\", \"default\", \"derive\", \"either1\", \"indexmap2\", \"jiff02\", \"preserve_order\", \"raw_value\", \"rust_decimal1\", \"schemars_derive\", \"semver1\", \"smallvec1\", \"smol_str02\", \"std\", \"url2\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2040997289075261528, "path": 11749559102293743368, "deps": [[9122563107207267705, "dyn_clone", false, 8788806922969602909], [9689903380558560274, "serde", false, 746175189569263742], [13535282280064720520, "ref_cast", false, 1670483668418139905], [14624475729786384435, "schemars_derive", false, 15426231542367993730], [15367738274754116744, "serde_json", false, 4917936035312756425]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\schemars-85515db1053e5a0a\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}