use serde::Deserialize;
use schemars::JsonSchema;
use std::collections::HashMap;

#[derive(Debug, <PERSON>lone, Deserialize, JsonSchema)]
pub struct BashToolParams {
    /// The bash command to execute
    pub command: String,
    /// Timeout in seconds (optional, None = no timeout)
    pub timeout: Option<u32>,
    /// Working directory for command execution (optional)
    pub working_dir: Option<String>,
    /// Environment variables to set (optional)
    #[serde(default)]
    pub env: HashMap<String, String>,
}
