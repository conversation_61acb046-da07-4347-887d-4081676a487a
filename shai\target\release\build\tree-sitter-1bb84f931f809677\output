cargo:rerun-if-env-changed=CARGO_FEATURE_WASM
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\alloc.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\alloc.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\array.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\atomic.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\clock.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\error_costs.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\get_changed_ranges.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\get_changed_ranges.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\host.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\language.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\language.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\length.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\lexer.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\lexer.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\lib.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\node.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\parser.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\parser.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\point.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\query.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\reduce_action.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\reusable_node.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\stack.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\stack.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\subtree.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\subtree.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\tree.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\tree.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\tree_cursor.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\tree_cursor.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\ts_assert.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\unicode
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\unicode.h
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\wasm
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\wasm_store.c
cargo:rerun-if-changed=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\src\wasm_store.h
OUT_DIR = Some(D:\Sandeep\Npm Packages\shai-main\shai\target\release\build\tree-sitter-1bb84f931f809677\out)
OPT_LEVEL = Some(3)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(D:\Sandeep\Npm Packages\shai-main\shai\target\release\deps;D:\Sandeep\Npm Packages\shai-main\shai\target\release;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Python310\;C:\Python310\Scripts\;C:\Program Files\Go\bin;C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\tools\ruby33\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\ProgramData\chocolatey\bin;D:\Sandeep\stdy\sdk tool;C:\Program Files\Go\bin;C:\Program Files\Git\cmd;C:\Python310;C:\Python310\Scripts;C:\Program Files\Java\jdk1.8.0_211\bin;C:\Program Files\nodejs\;C:\Users\<USER>\.pyenv\pyenv-win\bin;C:\Users\<USER>\.pyenv\pyenv-win\shims;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\msys64\ucrt64\bin;C:\ProgramData\chocolatey\lib\mingw\tools\install\mingw64\bin\lib\rust\tools\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;D:\Sandeep\cursor_data\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\go\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\scripts;D:\Sandeep\Windsurf\Windsurf\bin;C:\BuildTools\VC\Tools\MSVC\*\bin\Hostx64\x64;C:\Users\<USER>\.cargo\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Python310\;C:\Python310\Scripts\;C:\Program Files\Go\bin;C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\ProgramData\chocolatey\bin;D:\Sandeep\stdy\sdk tool;C:\Program Files\Go\bin;C:\Program Files\Git\cmd;C:\Python310;C:\Python310\Scripts;C:\Program Files\Java\jdk1.8.0_211\bin;C:\Program Files\nodejs\;C:\Users\<USER>\.pyenv\pyenv-win\bin;C:\Users\<USER>\.pyenv\pyenv-win\shims;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\msys64\ucrt64\bin;C:\ProgramData\clara-global-alias;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\go\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\scripts;D:\Sandeep\Windsurf\Windsurf\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\bin)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CC_x86_64-pc-windows-msvc
CC_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_x86_64_pc_windows_msvc
CC_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(false)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
OUT_DIR = Some(D:\Sandeep\Npm Packages\shai-main\shai\target\release\build\tree-sitter-1bb84f931f809677\out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-pc-windows-msvc)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
OUT_DIR = Some(D:\Sandeep\Npm Packages\shai-main\shai\target\release\build\tree-sitter-1bb84f931f809677\out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-pc-windows-msvc)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
OUT_DIR = Some(D:\Sandeep\Npm Packages\shai-main\shai\target\release\build\tree-sitter-1bb84f931f809677\out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-pc-windows-msvc)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
OUT_DIR = Some(D:\Sandeep\Npm Packages\shai-main\shai\target\release\build\tree-sitter-1bb84f931f809677\out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-pc-windows-msvc)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
OUT_DIR = Some(D:\Sandeep\Npm Packages\shai-main\shai\target\release\build\tree-sitter-1bb84f931f809677\out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-pc-windows-msvc)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
lib.c
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-lib=static=tree-sitter
cargo:rustc-link-search=native=D:\Sandeep\Npm Packages\shai-main\shai\target\release\build\tree-sitter-1bb84f931f809677\out
cargo:include=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\include
